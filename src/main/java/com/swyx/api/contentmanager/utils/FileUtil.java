package com.swyx.api.contentmanager.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
public class FileUtil {
    private final static String uploadDir = "/tmp";

    public static String uploadFile(MultipartFile file) {
        if (file.isEmpty()) {
            log.error("--- SaveBookResponse 文件为空");
            return null;
        }
        Path filePath = null;
        try {
            Path path = Paths.get(uploadDir);
            if (!Files.exists(path)) {
                Files.createDirectories(path); // 创建目录
            }
            // 上传文件至腾讯云COS
            filePath = path.resolve(Objects.requireNonNull(file.getOriginalFilename()));
            file.transferTo(filePath); // 保存文件
            String fileName = filePath.getFileName().toString();

            // 处理中文文件名问题
            String safeFileName = generateSafeFileName(fileName);
            String absolutePath = filePath.toAbsolutePath().toString();

            log.info("原始文件名: {}, 处理后文件名: {}", fileName, safeFileName);

            // 使用安全的文件名上传到COS
            UploadCOS.putLocalFile(absolutePath, safeFileName);

            return UploadCOS.getFileUrl(safeFileName);
        } catch (IOException e) {
            log.error("上传失败: {}", e.getMessage());
            return null;
        } finally {
            // 无论成功还是失败，都删除临时文件
            if (filePath != null) {
                try {
                    Files.deleteIfExists(filePath);
                    log.info("临时文件已删除: {}", filePath);
                } catch (IOException e) {
                    log.error("删除临时文件失败: {}", e.getMessage());
                }
            }
        }
    }

    public static List<String> uploadFiles(MultipartFile[] files) {
        List<String> list = new ArrayList<>();
        for (MultipartFile file : files) {
            list.add(uploadFile(file));
        }
        return list;
    }

    /**
     * 生成安全的文件名，处理中文和特殊字符
     * @param originalFileName 原始文件名
     * @return 处理后的安全文件名
     */
    private static String generateSafeFileName(String originalFileName) {
        if (originalFileName == null || originalFileName.isEmpty()) {
            return "unknown_file_" + System.currentTimeMillis();
        }

        try {
            // 获取文件扩展名
            String extension = "";
            int dotIndex = originalFileName.lastIndexOf('.');
            if (dotIndex > 0) {
                extension = originalFileName.substring(dotIndex);
            }

            // 生成基于时间戳和随机ID的文件名
            String timestamp = String.valueOf(System.currentTimeMillis());
            String randomId = IdGenerator.generateRadomId().substring(0, 8);

            // 如果原文件名不包含中文和特殊字符，可以保留部分原始名称
            String baseName = originalFileName;
            if (dotIndex > 0) {
                baseName = originalFileName.substring(0, dotIndex);
            }

            // 检查文件名是否只包含英文字母、数字和常见符号
            if (baseName.matches("^[a-zA-Z0-9._-]+$")) {
                return baseName + "_" + randomId + extension;
            } else {
                // 如果包含中文或其他特殊字符，则使用时间戳和随机ID
                return "file_" + timestamp + "_" + randomId + extension;
            }
        } catch (Exception e) {
            log.error("处理文件名时出错: {}", e.getMessage());
            return "error_file_" + System.currentTimeMillis();
        }
    }
}
