package com.swyx.api.contentmanager.service.project;

import com.swyx.api.contentmanager.dao.*;
import com.swyx.api.contentmanager.dao.mapper.ProjectEntityMapper;
import com.swyx.api.contentmanager.dto.HomeInfoResponse;
import com.swyx.api.contentmanager.dto.ProjectDetailResponse;
import com.swyx.api.contentmanager.dto.ProjectListResponse;
import com.swyx.api.contentmanager.service.task.TaskService;
import com.swyx.api.contentmanager.service.user.UserService;
import com.swyx.api.contentmanager.utils.IdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProjectService {
    @Autowired
    private ProjectEntityMapper projectEntityMapper;

    @Autowired
    private TaskService taskService;
    @Autowired
    private UserService userService;

    public HomeInfoResponse getHomeInfo(String userId) {
        HomeInfoResponse homeInfoResponse = new HomeInfoResponse();
        List<TaskEntity> taskEntities = taskService.getTasksByUserId(userId);
        List<String> projectIds = taskEntities.stream()
                .map(TaskEntity::getProjectId)
                .toList();

        ProjectEntityExample example = new ProjectEntityExample();
        ProjectEntityExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdIn(projectIds);
        List<ProjectEntity> projectEntities = projectEntityMapper.selectByExample(example);

        AtomicInteger totalIssueCountSum = new AtomicInteger(0);
        AtomicInteger completeIssueCountSum = new AtomicInteger(0);
        taskEntities.forEach(taskEntity -> {
            totalIssueCountSum.addAndGet(taskEntity.getTotalIssueCount());
            completeIssueCountSum.addAndGet(taskEntity.getCompleteIssueCount());
        });

        homeInfoResponse.setTotalIssueCount(totalIssueCountSum.get());
        homeInfoResponse.setCompleteIssueCount(completeIssueCountSum.get());
        homeInfoResponse.setProjectList(projectEntities);
        homeInfoResponse.setTaskList(taskEntities);
        homeInfoResponse.setTaskCount(taskEntities.size());
        homeInfoResponse.setProjectCount(projectEntities.size());

        return homeInfoResponse;
    }

    public List<ProjectListResponse> getProjectList() {
        ProjectEntityExample example = new ProjectEntityExample();
        ProjectEntityExample.Criteria criteria = example.createCriteria();
        List<ProjectEntity> projectEntities = projectEntityMapper.selectByExampleWithBLOBs(example);
        if (projectEntities == null || projectEntities.isEmpty()) {
            return new ArrayList<>();
        }
        List<ProjectListResponse> projectListResponses = new ArrayList<>();
        for (ProjectEntity projectEntity : projectEntities) {
            ProjectListResponse projectListResponse = new ProjectListResponse();
            BeanUtils.copyProperties(projectEntity, projectListResponse);

            List<TaskEntity> tasks = taskService.getTasksByProjectId(projectEntity.getProjectId());
            AtomicInteger totalIssueCountSum = new AtomicInteger(0);
            AtomicInteger completeIssueCountSum = new AtomicInteger(0);
            List<String> reviewerAvatars = new ArrayList<>();

            tasks.forEach(task -> {
                Integer totalCount = Optional.ofNullable(task.getTotalIssueCount()).orElse(0);
                Integer completeCount = Optional.ofNullable(task.getCompleteIssueCount()).orElse(0);
                totalIssueCountSum.addAndGet(totalCount);
                completeIssueCountSum.addAndGet(completeCount);

                UserEntity user = userService.getUserById(task.getReviewerId());
                reviewerAvatars.add(user.getAvatar());
            });

            projectListResponse.setTotalIssueCount(totalIssueCountSum.get());
            projectListResponse.setCompleteIssueCount(completeIssueCountSum.get());
            projectListResponse.setReviewerAvatars(reviewerAvatars);

            projectListResponses.add(projectListResponse);
        }
        return projectListResponses;
    }

    public ProjectDetailResponse getProjectById(String projectId) {
        ProjectEntityExample example = new ProjectEntityExample();
        ProjectEntityExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(projectId);
        ProjectEntity projectEntity = projectEntityMapper.selectByExampleWithBLOBs(example).getFirst();
        if (projectEntity == null) {
            return null;
        }

        AtomicInteger totalIssueCountSum = new AtomicInteger(0);
        AtomicInteger completeIssueCountSum = new AtomicInteger(0);
        List<String> reviewerIds = new ArrayList<>();

        List<TaskEntity> tasks = taskService.getTasksByProjectId(projectId);
        tasks.forEach(task -> {
            Integer totalCount = Optional.ofNullable(task.getTotalIssueCount()).orElse(0);
            Integer completeCount = Optional.ofNullable(task.getCompleteIssueCount()).orElse(0);
            totalIssueCountSum.addAndGet(totalCount);
            completeIssueCountSum.addAndGet(completeCount);
            reviewerIds.add(task.getReviewerId());
        });

        List<UserEntity> reviewers = userService.getUsersByIds(reviewerIds);
        UserEntity createUser = userService.getUserById(projectEntity.getCreator());

        ProjectDetailResponse projectDetailResponse = new ProjectDetailResponse();
        BeanUtils.copyProperties(projectEntity, projectDetailResponse);

        projectDetailResponse.setTasks(tasks);
        projectDetailResponse.setReviewers(reviewers);
        projectDetailResponse.setCreateUser(createUser);
        projectDetailResponse.setTotalIssueCount(totalIssueCountSum.get());
        projectDetailResponse.setCompleteIssueCount(completeIssueCountSum.get());

        return projectDetailResponse;
    }

    public void saveProject(ProjectEntity projectEntity, List<TaskEntityWithBLOBs> tasks) {
        String projectId = "project-" + IdGenerator.generateRadomId();
        tasks.forEach(task -> { task.setProjectId(projectId); });
        taskService.saveTasks(tasks);

        projectEntity.setProjectId(projectId);
        projectEntity.setStatus((byte)0);
        projectEntity.setTaskCount(tasks.size());
        projectEntity.setReviewerCount(tasks.size());
        projectEntity.setCreatedTime(new Date());
        projectEntity.setUpdatedTime(new Date());
        projectEntityMapper.insert(projectEntity);
    }
}
