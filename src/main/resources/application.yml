# ?????
spring:
    application:
        name: ContentManager
    datasource:
        url: ***************************************************************************************
        username: ringlong
        password: mH0LFdgoJpVoT2iu
        driver-class-name: com.mysql.cj.jdbc.Driver
    servlet:
        multipart:
            max-file-size: 50MB      # ???????????1MB?
            max-request-size: 50MB   # ???????????10MB?
            location: /tmp           # ??????????=
    main:
        allow-circular-references: true

server:
    servlet:
        context-path: /api

file:
    upload-dir: /tmp

# MyBatis ??
mybatis:
    mapper-locations: classpath:mybatis/*.xml  # XML ??????
    type-aliases-package: com.swyx.api.contentmanager.dao     # ??????
    configuration:
        map-underscore-to-camel-case: true          # ??????
#        log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  # ?? SQL ???????